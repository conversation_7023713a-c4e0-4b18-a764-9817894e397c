use pyo3::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LibraryInfo {
    pub path: String,
    pub name: String,
    pub photo_count: u64,
    pub last_modified: i64,
    pub r#type: String, // "apple_photos" or "directory"
    pub exists: bool,
    pub error: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ThumbnailRequest {
    pub library_path: String,
    pub output_dir: String,
    pub days_back: i32,
    pub max_photos: i32,
    pub thumbnail_size: String, // "small", "medium", "large"
    pub quality: i32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromPyObject)]
pub struct PhotoInfo {
    pub uuid: String,
    pub filename: String,
    pub original_path: String,
    pub thumbnail_path: Option<String>,
    pub date_taken: i64, // Unix timestamp
    pub file_size: u64,
    pub width: u32,
    pub height: u32,
    pub mime_type: String,
    pub camera_model: Option<String>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
    pub hash_values: Option<HashMap<String, String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimilarityGroup {
    pub group_id: String,
    pub photos: Vec<PhotoInfo>,
    pub similarity_score: f32,
    pub start_time: i64,
    pub end_time: i64,
    pub photo_count: usize,
    pub duration_seconds: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressUpdate {
    pub current: u64,
    pub total: u64,
    pub percentage: f32,
    pub status: String,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilterConfig {
    pub date_start: Option<i64>,
    pub date_end: Option<i64>,
    pub min_size: Option<u64>,
    pub max_size: Option<u64>,
    pub file_types: Option<Vec<String>>,
}

#[derive(Debug, thiserror::Error)]
pub enum PhotoError {
    #[error("Invalid library path: {0}")]
    #[allow(dead_code)] // 保留以备将来使用
    InvalidLibraryPath(String),

    #[error("Python module error: {0}")]
    PythonError(String),

    #[error("Thumbnail generation failed: {0}")]
    #[allow(dead_code)] // 保留以备将来使用
    ThumbnailError(String),

    #[error("Database error: {0}")]
    #[allow(dead_code)] // 保留以备将来使用
    DatabaseError(String),

    #[error("File system error: {0}")]
    FileSystemError(String),

    #[error("Memory limit exceeded: {0}")]
    #[allow(dead_code)] // 保留以备将来使用
    MemoryLimitError(String),
}

impl From<pyo3::PyErr> for PhotoError {
    fn from(err: pyo3::PyErr) -> Self {
        PhotoError::PythonError(err.to_string())
    }
}

impl From<std::io::Error> for PhotoError {
    fn from(err: std::io::Error) -> Self {
        PhotoError::FileSystemError(err.to_string())
    }
}

impl serde::Serialize for PhotoError {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}
