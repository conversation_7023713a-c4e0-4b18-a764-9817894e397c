use pyo3::prelude::*;

/// Python模块初始化函数
#[pymodule]
fn photo_dedup(_py: Python, _m: &Bound<PyModule>) -> PyResult<()> {
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_module_initialization() {
        Python::with_gil(|py| {
            let module = PyModule::new(py, "photo_dedup").unwrap();
            let result = photo_dedup(py, &module);
            assert!(result.is_ok());
        });
    }
}
