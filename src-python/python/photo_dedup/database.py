"""
Database models for photo deduplication and thumbnail management.
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict

from sqlalchemy import <PERSON>olean, Column, DateTime, Float, ForeignKey, Integer, String, create_engine
from sqlalchemy.dialects.sqlite import <PERSON>SO<PERSON>
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker

Base = declarative_base()


class Library(Base):
    """Represents a photo library (Apple Photos or directory)."""

    __tablename__ = "libraries"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    path = Column(String, nullable=False, unique=True)
    library_type = Column(String, nullable=False)  # 'apple_photos' or 'directory'
    photo_count = Column(Integer, default=0)
    last_scan = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship to photos
    photos = relationship("Photo", back_populates="library")


class Photo(Base):
    """Represents a photo with metadata and paths."""

    __tablename__ = "photos"

    id = Column(Integer, primary_key=True)
    uuid = Column(String, nullable=False, unique=True)
    library_id = Column(Integer, ForeignKey("libraries.id"), nullable=False)
    filename = Column(String, nullable=False)
    original_path = Column(String, nullable=False)
    date_taken = Column(DateTime, nullable=False)
    file_size = Column(Integer, nullable=False)
    width = Column(Integer, nullable=False)
    height = Column(Integer, nullable=False)
    mime_type = Column(String, nullable=False)
    thumbnail_path = Column(String)  # Path to generated thumbnail
    camera_model = Column(String)
    latitude = Column(Float)
    longitude = Column(Float)
    hash_values = Column(JSON)  # JSON field for storing hash values

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    library = relationship("Library", back_populates="photos")
    similarity_groups = relationship("SimilarityGroup", secondary="photo_similarity_groups", back_populates="photos")


class SimilarityGroup(Base):
    """Represents a group of similar photos."""

    __tablename__ = "similarity_groups"

    id = Column(Integer, primary_key=True)
    group_id = Column(String, nullable=False, unique=True)
    similarity_score = Column(Float, nullable=False)
    time_range_start = Column(DateTime, nullable=False)
    time_range_end = Column(DateTime, nullable=False)
    photo_count = Column(Integer, nullable=False)
    duration_seconds = Column(Float, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship to photos
    photos = relationship("Photo", secondary="photo_similarity_groups", back_populates="similarity_groups")


class PhotoSimilarityGroup(Base):
    """Association table for many-to-many relationship between photos and similarity groups."""

    __tablename__ = "photo_similarity_groups"

    photo_id = Column(Integer, ForeignKey("photos.id"), primary_key=True)
    group_id = Column(Integer, ForeignKey("similarity_groups.id"), primary_key=True)


class Thumbnail(Base):
    """Represents a generated thumbnail."""

    __tablename__ = "thumbnails"

    id = Column(Integer, primary_key=True)
    photo_uuid = Column(String, ForeignKey("photos.uuid"), nullable=False, unique=True)
    thumbnail_path = Column(String, nullable=False)
    size = Column(Integer, nullable=False)
    quality = Column(Integer, nullable=False)
    width = Column(Integer, nullable=False)
    height = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship to photo
    photo = relationship("Photo")


def initialize_database(db_path: str = None) -> sessionmaker:
    """
    Initialize the database and return a session factory.

    Args:
        db_path: Path to the SQLite database file (默认使用临时目录)

    Returns:
        Session factory for creating database sessions
    """
    # 如果没有提供路径，使用临时目录
    if db_path is None:
        import tempfile
        import os
        temp_dir = tempfile.gettempdir()
        db_path = os.path.join(temp_dir, "photo_manager.db")

    # Create database engine
    engine = create_engine(f"sqlite:///{db_path}", echo=False)

    # Create all tables
    Base.metadata.create_all(engine)

    # Create session factory
    Session = sessionmaker(bind=engine)

    # 运行数据库迁移以确保模式是最新的
    session = Session()
    try:
        ensure_database_schema_updated(session)
    finally:
        session.close()

    return Session


def get_or_create_library(session, name: str, path: str, library_type: str) -> Library:
    """
    Get an existing library or create a new one.

    Args:
        session: Database session
        name: Library name
        path: Library path
        library_type: Type of library ('apple_photos' or 'directory')

    Returns:
        Library object
    """
    library = session.query(Library).filter_by(path=path).first()
    if not library:
        library = Library(name=name, path=path, library_type=library_type)
        session.add(library)
        session.commit()
    return library


def store_photo_info(session, library_id: int, photo_info: Dict[str, Any]) -> Photo:
    """
    Store photo information in the database.

    Args:
        session: Database session
        library_id: ID of the library
        photo_info: Dictionary containing photo information

    Returns:
        Photo object
    """
    try:
        # Check if photo already exists
        photo = session.query(Photo).filter_by(uuid=photo_info["uuid"]).first()

        if not photo:
            photo = Photo(
                uuid=photo_info["uuid"],
                library_id=library_id,
                filename=photo_info["filename"],
                original_path=photo_info["original_path"],
                date_taken=datetime.fromtimestamp(photo_info["date_taken"]),
                file_size=photo_info["file_size"],
                width=photo_info["width"],
                height=photo_info["height"],
                mime_type=photo_info["mime_type"],
                thumbnail_path=photo_info.get("thumbnail_path"),
                camera_model=photo_info.get("camera_model"),
                latitude=photo_info.get("latitude"),
                longitude=photo_info.get("longitude"),
                hash_values=photo_info.get("hash_values"),
            )
            session.add(photo)
        else:
            # Update existing photo
            photo.library_id = library_id
            photo.filename = photo_info["filename"]
            photo.original_path = photo_info["original_path"]
            photo.date_taken = datetime.fromtimestamp(photo_info["date_taken"])
            photo.file_size = photo_info["file_size"]
            photo.width = photo_info["width"]
            photo.height = photo_info["height"]
            photo.mime_type = photo_info["mime_type"]
            photo.thumbnail_path = photo_info.get("thumbnail_path")
            photo.camera_model = photo_info.get("camera_model")
            photo.latitude = photo_info.get("latitude")
            photo.longitude = photo_info.get("longitude")
            photo.hash_values = photo_info.get("hash_values")
            photo.updated_at = datetime.utcnow()

        session.commit()
        return photo

    except Exception as e:
        # If there's any error, rollback and try to handle it gracefully
        session.rollback()

        # Try to find the existing photo again after rollback
        existing_photo = session.query(Photo).filter_by(uuid=photo_info["uuid"]).first()
        if existing_photo:
            # Photo exists, just return it
            return existing_photo
        else:
            # Re-raise the original exception if we can't find the photo
            raise e


def store_thumbnail_info(session, photo_uuid: str, thumbnail_info: Dict[str, Any]) -> Thumbnail:
    """
    Store thumbnail information in the database.

    Args:
        session: Database session
        photo_uuid: UUID of the photo
        thumbnail_info: Dictionary containing thumbnail information

    Returns:
        Thumbnail object
    """
    # Check if thumbnail already exists
    thumbnail = session.query(Thumbnail).filter_by(photo_uuid=photo_uuid).first()

    if not thumbnail:
        thumbnail = Thumbnail(photo_uuid=photo_uuid, thumbnail_path=thumbnail_info["thumbnail_path"], size=thumbnail_info["size"], quality=thumbnail_info["quality"], width=thumbnail_info["width"], height=thumbnail_info["height"])
        session.add(thumbnail)
    else:
        # Update existing thumbnail
        thumbnail.thumbnail_path = thumbnail_info["thumbnail_path"]
        thumbnail.size = thumbnail_info["size"]
        thumbnail.quality = thumbnail_info["quality"]
        thumbnail.width = thumbnail_info["width"]
        thumbnail.height = thumbnail_info["height"]

    session.commit()
    return thumbnail


def store_similarity_group(session, group_info: Dict[str, Any]) -> SimilarityGroup:
    """
    Store similarity group information in the database.

    Args:
        session: Database session
        group_info: Dictionary containing group information

    Returns:
        SimilarityGroup object
    """
    # Check if group already exists
    group = session.query(SimilarityGroup).filter_by(group_id=group_info["group_id"]).first()

    if not group:
        group = SimilarityGroup(
            group_id=group_info["group_id"], similarity_score=group_info["similarity_score"], time_range_start=datetime.fromtimestamp(group_info["start_time"]), time_range_end=datetime.fromtimestamp(group_info["end_time"]), photo_count=group_info["photo_count"], duration_seconds=group_info["duration_seconds"]
        )
        session.add(group)
        session.commit()

        # Associate photos with the group
        for photo_info in group_info["photos"]:
            photo = session.query(Photo).filter_by(uuid=photo_info["uuid"]).first()
            if photo:
                group.photos.append(photo)
    else:
        # Update existing group
        group.similarity_score = group_info["similarity_score"]
        group.time_range_start = datetime.fromtimestamp(group_info["start_time"])
        group.time_range_end = datetime.fromtimestamp(group_info["end_time"])
        group.photo_count = group_info["photo_count"]
        group.duration_seconds = group_info["duration_seconds"]
        group.photos.clear()  # Clear existing associations

        # Associate photos with the group
        for photo_info in group_info["photos"]:
            photo = session.query(Photo).filter_by(uuid=photo_info["uuid"]).first()
            if photo:
                group.photos.append(photo)

    session.commit()
    return group


def get_cached_date_range(session, library_path: str):
    """
    获取指定库的已缓存时间范围

    Args:
        session: 数据库会话
        library_path: 库路径

    Returns:
        DateRangeCache对象，如果不存在则返回None
    """
    return session.query(DateRangeCache).filter_by(library_path=library_path).first()


def get_cached_photos_in_date_range(session, library_path: str, start_datetime: datetime, end_datetime: datetime) -> list:
    """
    获取指定日期范围内的缓存照片

    Args:
        session: 数据库会话
        library_path: 库路径
        start_datetime: 开始日期时间
        end_datetime: 结束日期时间

    Returns:
        PhotoInfo对象列表
    """
    from .models import PhotoInfo

    library = session.query(Library).filter_by(path=library_path).first()
    if not library:
        return []

    # 查询指定日期范围内的照片
    photos = session.query(Photo).filter(
        Photo.library_id == library.id,
        Photo.date_taken >= start_datetime,
        Photo.date_taken <= end_datetime
    ).order_by(Photo.date_taken.desc()).all()

    # 转换为PhotoInfo对象列表
    photo_list = []
    for photo in photos:
        # 处理日期时间，确保有时区信息
        if hasattr(photo.date_taken, 'tzinfo'):
            # 如果是datetime对象
            if photo.date_taken.tzinfo is None:
                # 如果没有时区信息，假设为UTC
                date_taken = photo.date_taken.replace(tzinfo=timezone.utc)
            else:
                date_taken = photo.date_taken
        elif isinstance(photo.date_taken, (int, float)):
            # 如果是时间戳，转换为UTC datetime
            date_taken = datetime.fromtimestamp(photo.date_taken, tz=timezone.utc)
        else:
            # 如果是字符串，尝试解析
            try:
                dt = datetime.fromisoformat(str(photo.date_taken).replace('Z', '+00:00'))
                date_taken = dt
            except:
                date_taken = datetime.fromtimestamp(0, tz=timezone.utc)  # 默认值

        # 构建位置信息
        location = None
        if photo.latitude and photo.longitude:
            location = {
                'latitude': photo.latitude,
                'longitude': photo.longitude
            }

        # 创建PhotoInfo对象
        photo_info = PhotoInfo(
            uuid=photo.uuid or '',
            filename=photo.filename or '',
            original_path=photo.original_path or '',
            date_taken=date_taken,
            file_size=photo.file_size or 0,
            width=photo.width or 0,
            height=photo.height or 0,
            mime_type=photo.mime_type or '',
            thumbnail_path=photo.thumbnail_path or '',
            camera_model=photo.camera_model or '',
            location=location,
            hash_values=photo.hash_values or {}
        )
        photo_list.append(photo_info)

    return photo_list


def is_date_range_cached(session, library_path: str, start_datetime: datetime, end_datetime: datetime) -> bool:
    """
    检查指定日期范围是否已完全缓存

    Args:
        session: 数据库会话
        library_path: 库路径
        start_datetime: 开始日期时间
        end_datetime: 结束日期时间

    Returns:
        bool: 如果日期范围已完全缓存则返回True，否则返回False
    """
    cache = get_cached_date_range(session, library_path)
    if not cache:
        return False

    # 确保时区一致性
    cache_start = cache.start_date
    cache_end = cache.end_date

    # 如果缓存的日期没有时区信息，添加UTC时区
    if cache_start.tzinfo is None:
        cache_start = cache_start.replace(tzinfo=timezone.utc)
    if cache_end.tzinfo is None:
        cache_end = cache_end.replace(tzinfo=timezone.utc)

    # 如果输入的日期没有时区信息，添加UTC时区
    if start_datetime.tzinfo is None:
        start_datetime = start_datetime.replace(tzinfo=timezone.utc)
    if end_datetime.tzinfo is None:
        end_datetime = end_datetime.replace(tzinfo=timezone.utc)

    # 检查请求的日期范围是否在缓存范围内
    return (cache_start <= start_datetime and cache_end >= end_datetime)


def update_date_range_cache(session, library_path: str, new_start_date, new_end_date, photo_count: int = 0):
    """
    更新或创建时间范围缓存，保持连续性

    Args:
        session: 数据库会话
        library_path: 库路径
        new_start_date: 新的开始日期（可以是datetime对象或Unix时间戳）
        new_end_date: 新的结束日期（可以是datetime对象或Unix时间戳）
        photo_count: 新增的照片数量

    Returns:
        更新后的DateRangeCache对象
    """
    # 处理时间戳转换
    if isinstance(new_start_date, (int, float)):
        new_start_date = datetime.fromtimestamp(new_start_date, tz=timezone.utc)
    if isinstance(new_end_date, (int, float)):
        new_end_date = datetime.fromtimestamp(new_end_date, tz=timezone.utc)

    cache = get_cached_date_range(session, library_path)

    if not cache:
        # 创建新的缓存记录
        cache = DateRangeCache(
            library_path=library_path,
            start_date=new_start_date,
            end_date=new_end_date,
            photo_count=photo_count
        )
        session.add(cache)
    else:
        # 更新现有缓存，扩展时间范围
        # 确保缓存中的日期也有时区信息
        cache_start = cache.start_date
        cache_end = cache.end_date

        if cache_start.tzinfo is None:
            cache_start = cache_start.replace(tzinfo=timezone.utc)
            cache.start_date = cache_start
        if cache_end.tzinfo is None:
            cache_end = cache_end.replace(tzinfo=timezone.utc)
            cache.end_date = cache_end

        if new_start_date < cache_start:
            cache.start_date = new_start_date
        if new_end_date > cache_end:
            cache.end_date = new_end_date
        cache.photo_count += photo_count
        cache.last_updated = datetime.utcnow()

    session.commit()
    return cache


def get_next_date_range_to_fetch(session, library_path: str, days_to_fetch: int = 5) -> tuple:
    """
    获取下一个需要获取的时间范围

    Args:
        session: 数据库会话
        library_path: 库路径
        days_to_fetch: 要获取的天数

    Returns:
        (start_date, end_date) 元组，表示下一个要获取的时间范围
    """
    cache = get_cached_date_range(session, library_path)

    if not cache:
        # 如果没有缓存，从当前时间开始往前获取
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_to_fetch)
        return start_date, end_date
    else:
        # 如果有缓存，从最早日期继续往前获取
        end_date = cache.start_date - timedelta(seconds=1)  # 避免重复
        start_date = end_date - timedelta(days=days_to_fetch)
        return start_date, end_date


def load_cached_photos(session, library_path: str, limit: int = None) -> list:
    """
    从数据库加载已缓存的照片数据

    Args:
        session: 数据库会话
        library_path: 库路径
        limit: 限制返回的照片数量

    Returns:
        PhotoInfo对象列表
    """
    from .models import PhotoInfo

    library = session.query(Library).filter_by(path=library_path).first()
    if not library:
        return []

    query = session.query(Photo).filter_by(library_id=library.id).order_by(Photo.date_taken.desc())

    if limit:
        query = query.limit(limit)

    photos = query.all()

    # 转换为PhotoInfo对象列表
    photo_list = []
    for photo in photos:
        # 处理日期时间，确保有时区信息
        if hasattr(photo.date_taken, 'tzinfo'):
            # 如果是datetime对象
            if photo.date_taken.tzinfo is None:
                # 如果没有时区信息，假设为UTC
                date_taken = photo.date_taken.replace(tzinfo=timezone.utc)
            else:
                date_taken = photo.date_taken
        elif isinstance(photo.date_taken, (int, float)):
            # 如果是时间戳，转换为UTC datetime
            date_taken = datetime.fromtimestamp(photo.date_taken, tz=timezone.utc)
        else:
            # 如果是字符串，尝试解析
            try:
                dt = datetime.fromisoformat(str(photo.date_taken).replace('Z', '+00:00'))
                date_taken = dt
            except:
                date_taken = datetime.fromtimestamp(0, tz=timezone.utc)  # 默认值

        # 构建位置信息
        location = None
        if photo.latitude and photo.longitude:
            location = {
                'latitude': photo.latitude,
                'longitude': photo.longitude
            }

        # 创建PhotoInfo对象
        photo_info = PhotoInfo(
            uuid=photo.uuid or '',
            filename=photo.filename or '',
            original_path=photo.original_path or '',
            date_taken=date_taken,
            file_size=photo.file_size or 0,
            width=photo.width or 0,
            height=photo.height or 0,
            mime_type=photo.mime_type or '',
            thumbnail_path=photo.thumbnail_path or '',
            camera_model=photo.camera_model or '',
            location=location,
            hash_values=photo.hash_values or {}
        )
        photo_list.append(photo_info)

    return photo_list


def clear_database_cache(db_path: str = None) -> str:
    """
    清空数据库缓存

    Args:
        db_path: 数据库路径，如果为None则使用默认路径

    Returns:
        str: 操作结果消息
    """
    import os
    import logging

    logger = logging.getLogger(__name__)

    try:
        # 确定数据库路径
        if db_path is None:
            import tempfile
            db_path = os.path.join(tempfile.gettempdir(), "photo_manager.db")

        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            message = f"数据库文件不存在: {db_path}"
            logger.info(message)
            return message

        # 获取文件大小（用于统计）
        file_size = os.path.getsize(db_path)
        file_size_mb = file_size / (1024 * 1024)

        # 删除数据库文件
        os.remove(db_path)

        message = f"✅ 成功清空数据库缓存: {db_path} (释放 {file_size_mb:.2f} MB)"
        logger.info(message)
        return message

    except Exception as e:
        error_message = f"❌ 清空数据库缓存失败: {str(e)}"
        logger.error(error_message)
        return error_message


class PhotoHashCache(Base):
    """Cache table for photo hash values with performance metrics."""

    __tablename__ = "photo_hash_cache"

    id = Column(Integer, primary_key=True)
    photo_uuid = Column(String, nullable=False, unique=True)
    filename = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_size = Column(Integer)
    file_mtime = Column(Float)  # File modification time as timestamp

    # Hash values
    ahash = Column(String)
    dhash = Column(String)
    phash = Column(String)
    whash = Column(String)

    # Performance metrics (in milliseconds)
    ahash_time_ms = Column(Float)
    dhash_time_ms = Column(Float)
    phash_time_ms = Column(Float)
    whash_time_ms = Column(Float)
    total_time_ms = Column(Float)

    # Cache metadata
    cache_hit = Column(Integer, default=0)  # Number of cache hits
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class AHashIndex(Base):
    """Fast lookup index for AHash values for first-stage filtering."""

    __tablename__ = "ahash_index"

    id = Column(Integer, primary_key=True)
    photo_uuid = Column(String, ForeignKey("photo_hash_cache.photo_uuid"), nullable=False)
    ahash = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)


class PerformanceMetrics(Base):
    """Performance metrics for batch processing operations."""

    __tablename__ = "performance_metrics"

    id = Column(Integer, primary_key=True)
    operation_type = Column(String, nullable=False)  # 'batch_hash', 'similarity_detection', etc.
    photo_count = Column(Integer, nullable=False)
    total_time_ms = Column(Float, nullable=False)
    avg_time_per_photo_ms = Column(Float, nullable=False)

    # Detailed timing breakdown
    ahash_total_ms = Column(Float)
    dhash_total_ms = Column(Float)
    phash_total_ms = Column(Float)
    whash_total_ms = Column(Float)

    # Resource usage
    cpu_cores_used = Column(Integer)
    memory_peak_mb = Column(Float)

    # Cache statistics
    cache_hits = Column(Integer, default=0)
    cache_misses = Column(Integer, default=0)
    cache_hit_rate = Column(Float)

    created_at = Column(DateTime, default=datetime.utcnow)


class DateRangeCache(Base):
    """记录已获取照片的时间范围，支持连续性更新"""

    __tablename__ = "date_range_cache"

    id = Column(Integer, primary_key=True)
    library_path = Column(String, nullable=False)
    start_date = Column(DateTime, nullable=False)  # 最早的日期
    end_date = Column(DateTime, nullable=False)    # 最晚的日期
    photo_count = Column(Integer, default=0)       # 该范围内的照片数量
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<DateRangeCache(library='{self.library_path}', range='{self.start_date}' to '{self.end_date}', photos={self.photo_count})>"



