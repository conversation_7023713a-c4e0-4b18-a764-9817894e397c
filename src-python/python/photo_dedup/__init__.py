"""
Photo Deduplication and Thumbnail Management Module

This module provides core functionality for photo collection, thumbnail generation,
and temporal analysis for the Tauri v2 + PyO3 thumbnail manager application.
"""

__version__ = "0.1.0"
__author__ = "Photo Thumbnail Manager Team"

from .collector import (
    collect_photos_from_library,
    collect_photos_from_directory,
    validate_library_path,
    get_library_info
)
from .thumbnail import generate_thumbnails_batch, ThumbnailConfig, get_thumbnail_stats, cleanup_thumbnails
from .analyzer import detect_temporal_groups, calculate_similarity_score, generate_hash_values, analyze_photo_collection
from .database import initialize_database, get_or_create_library, store_photo_info, store_thumbnail_info, store_similarity_group
from .integrator import process_photo_library, get_processing_summary
from .cache import Cache, get_cache, cache_result
from .models import PhotoInfo, ThumbnailInfo, SimilarityGroup, ThumbnailConfig

__all__ = [
    "collect_photos_from_library",
    "collect_photos_from_directory",
    "validate_library_path",
    "get_library_info",
    "generate_thumbnails_batch",
    "get_thumbnail_stats",
    "cleanup_thumbnails",
    "detect_temporal_groups",
    "calculate_similarity_score",
    "generate_hash_values",
    "analyze_photo_collection",
    "initialize_database",
    "get_or_create_library",
    "store_photo_info",
    "store_thumbnail_info",
    "store_similarity_group",
    "process_photo_library",
    "get_processing_summary",
    "Cache",
    "get_cache",
    "cache_result",
    "PhotoInfo",
    "ThumbnailInfo",
    "SimilarityGroup",
    "ThumbnailConfig",
]